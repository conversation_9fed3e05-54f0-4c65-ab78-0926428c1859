import { useTranslation } from 'react-i18next';
import { useRef, useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { AnimatePresence, motion } from 'framer-motion';
import { cn } from '@/Common/function/utils.ts';
import { driverApi, dispatchApi } from '@/api';
import {
  BreakdownStatusType,
  DemoTest,
  DriverStatusType,
  DropdownOption,
  OperationStatusType,
  toBreakdownStatusType,
  toDriverStatusType,
  toOperationStatusType,
  toStatusLabel,
} from '@/types';
import DriverSearchPopover, {
  DriverInfo,
} from './components/DriverSearchPopover';
import H2Title from '@/Common/Components/common/H2Title';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import { Button } from '@/Common/Components/common/Button';
import ItinerarySection from './components/ItinerarySection';
import { AddressType } from './components/SortableItem';
import VechicleDropDownPaginated from './components/VehicleDropDownPaginated';
import Input from '@/Common/Components/common/Input';
import arrow from '@/assets/images/arrow/arrow_left_m.svg';
import { AdminItineraryDispatchCreateReqDTO } from '@/api/generated';

type DriverInfoParams = {
  driverName: string;
  page: number;
  size: number;
  sort: string;
};

type EquipmentParams = {
  driverId: number;
  page: number;
  size: number;
  sort: string;
};

const DispatchSearch = ({
  onRouteChange,
}: {
  onRouteChange?: (
    route: {
      routeResult: unknown;
      waypoints: [number, number][];
    } | null,
  ) => void;
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  // 기사 상태
  const statusMap = {
    onDuty: { color: 'bg-semantic-1', label: t('OnDuty') },
    idle: { color: 'bg-semantic-3', label: t('Idle') },
  };

  // type VehicleOption = {
  //   key: string;
  //   value: string;
  //   label: string;
  //   subLabel: string;
  //   status: string;
  //   statusLabel: string;
  // };

  // 일정 제목 상태 관리
  const [itineraryTitle, setItineraryTitle] = useState('');

  // 선택된 날짜 상태 관리
  const [selectedDate, setSelectedDate] = useState(
    dayjs().format('YYYY-MM-DD'),
  );

  // 기사 검색 상태 관리
  const [selectedDriver, setSelectedDriver] = useState<DriverInfo | null>(null);

  // 차량 검색 상태 관리
  const [selectedVehicle, setSelectedVehicle] = useState<{
    key: string;
    value: string;
  } | null>(null);

  // 주소 선택 상태 관리 (각 itinerary item별로 실제 주소 객체 저장)
  const [selectedAddresses, setSelectedAddresses] = useState<{
    [itemIndex: number]: AddressType | null;
  }>({});

  // 기사 선택 처리
  const handleDriverSelect = (driver: DriverInfo | null) => {
    setSelectedDriver(driver);
    // 기사 선택 해제 시 검색 파라미터 초기화하여 다시 검색 가능하게 함
    if (!driver) {
      setDriverInfosParams({
        ...driverInfosParams,
        driverName: '',
      });
    }
  };

  // Dispatch 버튼 클릭 핸들러
  const handleDispatch = async () => {
    console.log('=== Dispatch 정보 ===');
    console.log('일정 제목:', itineraryTitle);
    console.log('선택된 날짜:', selectedDate);
    console.log('선택된 기사:', selectedDriver);
    console.log('선택된 차량:', selectedVehicle);
    console.log('선택된 주소 정보:', selectedAddresses);
    console.log(DispatchSearch);

    // 필수 필드 검증
    if (!itineraryTitle.trim()) {
      alert('일정 제목을 입력해주세요.');
      return;
    }
    if (!selectedDate) {
      alert('날짜를 선택해주세요.');
      return;
    }
    if (!selectedDriver) {
      alert('기사를 선택해주세요.');
      return;
    }
    if (!selectedVehicle) {
      alert('차량을 선택해주세요.');
      return;
    }

    // selectedAddresses를 destinations 배열로 변환
    const destinations = Object.entries(selectedAddresses)
      .filter(([_, addressObj]) => addressObj !== null)
      .map(([_, addressObj]) => ({
        destinationName: addressObj!.address,
        destinationAddress: addressObj!.address,
        destinationLocation: {
          x: addressObj!.lng, // 경도
          y: addressObj!.lat, // 위도
        },
      }));

    if (destinations.length === 0) {
      alert('최소 하나의 목적지를 선택해주세요.');
      return;
    }

    try {
      const body: AdminItineraryDispatchCreateReqDTO = {
        itineraryPlanName: itineraryTitle,
        scheduledDt: selectedDate,
        driverId: selectedDriver.driverId,
        equipmentId: Number(selectedVehicle.value),
        destinations,
      };

      const response = await dispatchApi.createAdminItineraryDispatch({
        adminItineraryDispatchCreateReqDTO: body,
      });
      console.log('배차 생성 성공:', response);

      // 성공 시 사용자에게 알림 (실제 프로젝트에서는 toast나 modal 사용 권장)
      alert('배차가 성공적으로 생성되었습니다.');

      // 필요시 페이지 리다이렉트나 상태 초기화 등 추가 처리
    } catch (error) {
      console.error('API 호출 에러:', error);
      // 사용자에게 에러 메시지 표시 (실제 프로젝트에서는 toast나 modal 사용 권장)
      alert('배차 생성 중 오류가 발생했습니다. 다시 시도해주세요.');
    }
  };

  /** Params */
  const [driverInfosParams, setDriverInfosParams] = useState<DriverInfoParams>({
    driverName: '',
    page: 0,
    size: 10,
    sort: 'driverName,asc',
  });

  const [equipmentInfosParams, setEquipmentInfosParams] =
    useState<EquipmentParams>({
      driverId: 0,
      page: 0,
      size: 10,
      sort: 'modelName,asc',
    });

  /** useQuery */
  const { data: driverInfos } = useQuery<DriverInfo[] | null>({
    queryKey: ['/api/driver/page', driverInfosParams],
    queryFn: async () => {
      try {
        const response = await driverApi.getAdminDriverPage(driverInfosParams);
        if (response.data && response.data.content && response.data.page) {
          const result: DriverInfo[] = [];
          response.data.content.forEach((row) => {
            if (row.driverId !== undefined) {
              result.push({
                driverId: row.driverId,
                driverName: row.driverName ?? '',
                phoneNumber: row.driverPhone ?? '',
                status: toDriverStatusType(row.driverStatus ?? 'idle'),
              });
            }
          });
          //status가 'idle'을 먼저 정렬
          result.sort((a, b) => {
            if (
              a.status === DriverStatusType.Idle &&
              b.status !== DriverStatusType.Idle
            ) {
              return -1; // a가 먼저 오도록
            }
            if (
              b.status === DriverStatusType.Idle &&
              a.status !== DriverStatusType.Idle
            ) {
              return 1; // b가 먼저 오도록
            }
            return 0; // 동일한 상태면 순서 유지
          });
          return result;
        }
        return null;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    enabled: !selectedDriver, // 기사가 선택되지 않았을 때만 API 호출
  });

  const equipmentOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      try {
        if (equipmentInfosParams.driverId > 0) {
          const response = await driverApi.getAdminEquipmentPageOfDriver({
            driverId: equipmentInfosParams.driverId,
            page: page,
            size: 100,
            sort: 'modelName,asc',
          });
          if (response.data && response.data.content && response.data.page) {
            const result: DropdownOption[] = [];
            response.data.content.forEach((row) => {
              if (row.equipmentId !== undefined) {
                const operationStatusType = toOperationStatusType(
                  row.status?.operationStatus,
                );
                const breakdownStatusType = toBreakdownStatusType(
                  row.status?.breakdownStatus,
                );
                const status =
                  breakdownStatusType == BreakdownStatusType.None
                    ? operationStatusType
                    : breakdownStatusType;
                const statusLabel = toStatusLabel(status);

                result.push({
                  key: row.modelName ?? '',
                  value: row.equipmentId.toString(),
                  subLabel: row.plateNo ?? '',
                  status: status,
                  statusLabel: statusLabel,
                });
              }
            });
            return result;
          }
        }
        return [];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [equipmentInfosParams],
  );

  useEffect(() => {
    setSelectedVehicle(null); // 기사 변경 시 차량 초기화
    setEquipmentInfosParams((prev) => ({
      ...prev,
      driverId: selectedDriver?.driverId ?? 0,
      page: 0, // 페이지 초기화
    }));
  }, [selectedDriver]);

  return (
    <article>
      <motion.button
        key="dispatch-btn"
        type="button"
        initial={false}
        animate={{
          left: isOpen ? 360 : 0,
          opacity: 1,
          x: 0,
        }}
        transition={{ duration: 0.4, ease: 'easeInOut' }}
        className={cn('py-2 bg-secondary-6 rounded-r absolute top-2 z-10')}
        aria-label={isOpen ? t('Close') : t('Open')}
        onClick={() => setIsOpen((prev) => !prev)}
        style={{ left: isOpen ? 360 : 0 }}
      >
        <img
          src={arrow}
          alt="arrow"
          className={cn(
            'transition-transform duration-200',
            !isOpen && 'rotate-180',
          )}
        />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            key="dispatch-list"
            initial={{ opacity: 1, x: -360 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 1, x: -360 }}
            transition={{ duration: 0.4, ease: 'easeInOut' }}
            className="w-[360px] h-full bg-white absolute top-0 left-0 z-10"
          >
            <H2Title>{t('Dispatch')}</H2Title>

            {/* search */}
            <div className="mx-5 py-[30px] space-y-3 border-b border-gray-6">
              <Input
                placeholder={t('ItineraryTitle')}
                value={itineraryTitle}
                onChange={(e) => setItineraryTitle(e.target.value)}
              />
              {/* 날짜 선택 */}
              <DaySelector
                initValue={selectedDate}
                size="lg"
                onChange={(date) =>
                  setSelectedDate(dayjs(date).format('YYYY-MM-DD'))
                }
              />
              {/* 기사 검색 */}
              <DriverSearchPopover
                placeholder={t('Driver')}
                classNames={{
                  content:
                    '!p-[5px] w-[320px] max-h-[300px] space-y-[5px] bg-white border border-gray-6 rounded-md overflow-y-auto',
                }}
                onSearch={async (query) => {
                  setDriverInfosParams({
                    ...driverInfosParams,
                    driverName: query,
                  }); // useQuery의 queryKey를 업데이트
                  return driverInfos || []; // useQuery에서 가져온 데이터를 반환
                }}
                onSelect={handleDriverSelect}
                renderResult={(driver) => {
                  const status =
                    driver.status == DriverStatusType.Idle
                      ? statusMap.idle
                      : statusMap.onDuty;
                  const isDisabled = driver.status != DriverStatusType.Idle;

                  return (
                    <div
                      className={cn(
                        'py-[10px] px-[15px] space-y-[3px] body4 rounded-md',
                        isDisabled
                          ? 'bg-gray-200 cursor-not-allowed'
                          : 'hover:bg-primary-0 cursor-pointer',
                      )}
                    >
                      <div className="f-c-b">
                        <h3 className="min-w-[200px] body4">
                          {driver.driverName}
                        </h3>
                        <p className="f-c gap-[5px] caption4">
                          <span
                            className={cn('w-2 h-2 rounded-full', status.color)}
                          />
                          {status.label}
                        </p>
                      </div>
                      <p className="body6">{driver.phoneNumber}</p>
                    </div>
                  );
                }}
              />

              {/* 차량 선택 */}
              <VechicleDropDownPaginated
                key={`vechicle-${equipmentInfosParams.driverId}`} // 운전자 변경 시 리렌더링
                loadOptions={equipmentOptions}
                placeholder={t('Vehicle')}
                selectedKey={selectedVehicle?.key ?? ''}
                onSelPair={(key: string, value: string) => {
                  setSelectedVehicle({ key: key, value: value });
                }}
                className="max-w-full mt-3"
              />
            </div>

            {/* content */}
            <div className="h-[calc(100%-337px)] py-5 px-5 f-c-b flex-col gap-5">
              <ItinerarySection
                onAddressChange={setSelectedAddresses}
                onRouteChange={onRouteChange}
              />

              {/* 배차 버튼 */}
              <Button
                variant="bt_primary"
                label={t('Dispatch')}
                className="w-full"
                onClick={handleDispatch}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </article>
  );
};

export default DispatchSearch;
